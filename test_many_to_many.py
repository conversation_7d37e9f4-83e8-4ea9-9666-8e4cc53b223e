#!/usr/bin/env python3
"""
Test script to verify the many-to-many relationship between FabricDefinition and Supplier
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_many_to_many_relationship():
    print("Testing Many-to-Many Relationship between FabricDefinition and Supplier")
    print("=" * 70)
    
    # First, let's get existing suppliers
    print("1. Fetching existing suppliers...")
    suppliers_response = requests.get(f"{BASE_URL}/suppliers/")
    if suppliers_response.status_code == 200:
        suppliers = suppliers_response.json()
        print(f"   Found {len(suppliers)} suppliers")
        for supplier in suppliers[:3]:  # Show first 3
            print(f"   - {supplier['name']} (ID: {supplier['supplier_id']})")
    else:
        print(f"   Error fetching suppliers: {suppliers_response.status_code}")
        return
    
    if len(suppliers) < 2:
        print("   Need at least 2 suppliers to test many-to-many relationship")
        return
    
    # Create a test fabric definition with multiple suppliers
    print("\n2. Creating fabric definition with multiple suppliers...")
    fabric_data = {
        "fabric_name": "Test Multi-Supplier Fabric",
        "supplier_ids": [suppliers[0]['supplier_id'], suppliers[1]['supplier_id']],
        "date_added": "2025-06-30"
    }
    
    fabric_response = requests.post(f"{BASE_URL}/fabric-definitions/", json=fabric_data)
    if fabric_response.status_code == 201:
        fabric = fabric_response.json()
        print(f"   ✅ Created fabric definition: {fabric['fabric_name']} (ID: {fabric['id']})")
        print(f"   Suppliers: {[s['name'] for s in fabric.get('suppliers', [])]}")
    else:
        print(f"   ❌ Error creating fabric: {fabric_response.status_code}")
        print(f"   Response: {fabric_response.text}")
        return
    
    # Fetch the created fabric to verify the relationship
    print("\n3. Verifying the created fabric definition...")
    fabric_detail_response = requests.get(f"{BASE_URL}/fabric-definitions/{fabric['id']}/")
    if fabric_detail_response.status_code == 200:
        fabric_detail = fabric_detail_response.json()
        print(f"   ✅ Fabric: {fabric_detail['fabric_name']}")
        print(f"   Suppliers: {fabric_detail.get('supplier_names', [])}")
        print(f"   Number of suppliers: {len(fabric_detail.get('suppliers', []))}")
    else:
        print(f"   ❌ Error fetching fabric detail: {fabric_detail_response.status_code}")
    
    # Test updating the fabric with different suppliers
    print("\n4. Testing fabric update with different suppliers...")
    if len(suppliers) >= 3:
        update_data = {
            "fabric_name": "Updated Multi-Supplier Fabric",
            "supplier_ids": [suppliers[0]['supplier_id'], suppliers[2]['supplier_id']],
            "date_added": "2025-06-30"
        }
        
        update_response = requests.put(f"{BASE_URL}/fabric-definitions/{fabric['id']}/", json=update_data)
        if update_response.status_code == 200:
            updated_fabric = update_response.json()
            print(f"   ✅ Updated fabric: {updated_fabric['fabric_name']}")
            print(f"   New suppliers: {[s['name'] for s in updated_fabric.get('suppliers', [])]}")
        else:
            print(f"   ❌ Error updating fabric: {update_response.status_code}")
            print(f"   Response: {update_response.text}")
    else:
        print("   Skipping update test - need at least 3 suppliers")
    
    # Clean up - delete the test fabric
    print("\n5. Cleaning up test data...")
    delete_response = requests.delete(f"{BASE_URL}/fabric-definitions/{fabric['id']}/")
    if delete_response.status_code == 204:
        print("   ✅ Test fabric deleted successfully")
    else:
        print(f"   ⚠️  Could not delete test fabric: {delete_response.status_code}")
    
    print("\n" + "=" * 70)
    print("Test completed!")

if __name__ == "__main__":
    try:
        test_many_to_many_relationship()
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the backend server.")
        print("   Make sure the Django server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ An error occurred: {e}")
