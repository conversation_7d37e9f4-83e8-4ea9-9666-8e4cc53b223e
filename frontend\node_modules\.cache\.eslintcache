[{"C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewCutting.js": "4", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddCutting.js": "5", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewProductList.js": "6", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewApproveProduct.js": "7", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\PackingReportChart.js": "8", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddPackingSession.js": "9", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddShop.js": "10", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\CreateOrder.js": "11", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\Login.js": "12", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OwnerDashboard.js": "13", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\InventoryDashboard.js": "14", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OrdersDashboard.js": "15", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesDashboard.js": "16", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\Signup.js": "17", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddSupplier.js": "18", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\EditFabric.js": "19", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabrics.js": "20", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabricVariants.js": "21", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddDailySewingRecord.js": "22", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddFabric.js": "23", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ApproveFinishedProduct.js": "24", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewDailySewingHistory.js": "25", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\OwnerNavBar.js": "26", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\InventoryManagerNavBar.js": "27", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\utils\\axiosConfig.js": "28", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingSessions.js": "29", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventory.js": "30", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventorySales.js": "31", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SellProductPage.js": "32", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OrderListPage.js.js": "33", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OwnerOrdersPage.js": "34", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesTeamOrdersPage.js": "35", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ShopAnalysisDashboard.js": "36", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OrderAnalysisPage.js": "37", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductView.js": "38", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductImageViewer.js": "39", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewSuppliers.js": "40", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\FabricInventoryDetail.js": "41", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\CuttingRecordDetail.js": "42", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\RoleBasedNavBar.js": "43", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\SalesTeamNavBar.js": "44", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\utils\\api.js": "45", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\InvoicePreviewModal.js": "46", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\OrderCoordinatorNavBar.js": "47", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\PaymentModal.js": "48", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\DeliveryModal.js": "49", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\ShopDistrictAnalysis.js": "50", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\utils\\auth.js": "51", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\DashboardCard.js": "52", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewShops.js": "53", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesReport.js": "54", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\ProtectedRoute.js": "55", "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\RevertOrderModal.js": "56"}, {"size": 706, "mtime": 1747392125347, "results": "57", "hashOfConfig": "58"}, {"size": 10319, "mtime": 1747671538957, "results": "59", "hashOfConfig": "58"}, {"size": 375, "mtime": 1747391048078, "results": "60", "hashOfConfig": "58"}, {"size": 33001, "mtime": 1747671538992, "results": "61", "hashOfConfig": "58"}, {"size": 33822, "mtime": 1747679345412, "results": "62", "hashOfConfig": "58"}, {"size": 33531, "mtime": 1747671538997, "results": "63", "hashOfConfig": "58"}, {"size": 99330, "mtime": 1747671538989, "results": "64", "hashOfConfig": "58"}, {"size": 2458, "mtime": 1747392125362, "results": "65", "hashOfConfig": "58"}, {"size": 14435, "mtime": 1747392125350, "results": "66", "hashOfConfig": "58"}, {"size": 24066, "mtime": 1747392125351, "results": "67", "hashOfConfig": "58"}, {"size": 50544, "mtime": 1747671538968, "results": "68", "hashOfConfig": "58"}, {"size": 14310, "mtime": 1747671538978, "results": "69", "hashOfConfig": "58"}, {"size": 79153, "mtime": 1747804930945, "results": "70", "hashOfConfig": "58"}, {"size": 42225, "mtime": 1747671538977, "results": "71", "hashOfConfig": "58"}, {"size": 14637, "mtime": 1747671538980, "results": "72", "hashOfConfig": "58"}, {"size": 6098, "mtime": 1747392125362, "results": "73", "hashOfConfig": "58"}, {"size": 26457, "mtime": 1751260851581, "results": "74", "hashOfConfig": "58"}, {"size": 13849, "mtime": 1747392125351, "results": "75", "hashOfConfig": "58"}, {"size": 20894, "mtime": 1747671538973, "results": "76", "hashOfConfig": "58"}, {"size": 29956, "mtime": 1747671538994, "results": "77", "hashOfConfig": "58"}, {"size": 44234, "mtime": 1747671538993, "results": "78", "hashOfConfig": "58"}, {"size": 33108, "mtime": 1747741856961, "results": "79", "hashOfConfig": "58"}, {"size": 15017, "mtime": 1747392125349, "results": "80", "hashOfConfig": "58"}, {"size": 56491, "mtime": 1747748693557, "results": "81", "hashOfConfig": "58"}, {"size": 12807, "mtime": 1747671538992, "results": "82", "hashOfConfig": "58"}, {"size": 7986, "mtime": 1747671538962, "results": "83", "hashOfConfig": "58"}, {"size": 8076, "mtime": 1747671538958, "results": "84", "hashOfConfig": "58"}, {"size": 1578, "mtime": 1747392125382, "results": "85", "hashOfConfig": "58"}, {"size": 8358, "mtime": 1747392125379, "results": "86", "hashOfConfig": "58"}, {"size": 48259, "mtime": 1747671538995, "results": "87", "hashOfConfig": "58"}, {"size": 111, "mtime": 1747671538996, "results": "88", "hashOfConfig": "58"}, {"size": 29017, "mtime": 1747392125371, "results": "89", "hashOfConfig": "58"}, {"size": 17459, "mtime": 1747392125358, "results": "90", "hashOfConfig": "58"}, {"size": 53281, "mtime": 1747671538985, "results": "91", "hashOfConfig": "58"}, {"size": 24077, "mtime": 1747392125370, "results": "92", "hashOfConfig": "58"}, {"size": 5738, "mtime": 1747392125372, "results": "93", "hashOfConfig": "58"}, {"size": 36946, "mtime": 1747392125357, "results": "94", "hashOfConfig": "58"}, {"size": 25701, "mtime": 1747392125369, "results": "95", "hashOfConfig": "58"}, {"size": 14826, "mtime": 1747392125364, "results": "96", "hashOfConfig": "58"}, {"size": 13919, "mtime": 1747671538999, "results": "97", "hashOfConfig": "58"}, {"size": 11398, "mtime": 1747392125355, "results": "98", "hashOfConfig": "58"}, {"size": 23403, "mtime": 1747671538972, "results": "99", "hashOfConfig": "58"}, {"size": 1843, "mtime": 1747392125341, "results": "100", "hashOfConfig": "58"}, {"size": 7639, "mtime": 1747671538964, "results": "101", "hashOfConfig": "58"}, {"size": 2374, "mtime": 1747392125381, "results": "102", "hashOfConfig": "58"}, {"size": 9995, "mtime": 1747671538959, "results": "103", "hashOfConfig": "58"}, {"size": 11014, "mtime": 1747671538960, "results": "104", "hashOfConfig": "58"}, {"size": 11059, "mtime": 1747392125337, "results": "105", "hashOfConfig": "58"}, {"size": 5758, "mtime": 1747392125330, "results": "106", "hashOfConfig": "58"}, {"size": 6283, "mtime": 1747392125342, "results": "107", "hashOfConfig": "58"}, {"size": 2550, "mtime": 1747392125382, "results": "108", "hashOfConfig": "58"}, {"size": 1611, "mtime": 1747392125329, "results": "109", "hashOfConfig": "58"}, {"size": 8673, "mtime": 1747671538998, "results": "110", "hashOfConfig": "58"}, {"size": 26457, "mtime": 1747805379124, "results": "111", "hashOfConfig": "58"}, {"size": 1939, "mtime": 1747671538963, "results": "112", "hashOfConfig": "58"}, {"size": 6549, "mtime": 1747671538963, "results": "113", "hashOfConfig": "58"}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gcje12", {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewCutting.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddCutting.js", ["282"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewProductList.js", ["283"], ["284"], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewApproveProduct.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\PackingReportChart.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddPackingSession.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddShop.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\CreateOrder.js", ["285", "286", "287"], ["288"], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\Login.js", ["289"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OwnerDashboard.js", [], ["290"], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\InventoryDashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OrdersDashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesDashboard.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\Signup.js", ["291", "292", "293"], ["294"], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddSupplier.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\EditFabric.js", ["295", "296"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabrics.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewFabricVariants.js", ["297", "298", "299", "300"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddDailySewingRecord.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\AddFabric.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ApproveFinishedProduct.js", ["301", "302", "303", "304", "305"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewDailySewingHistory.js", ["306"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\OwnerNavBar.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\InventoryManagerNavBar.js", ["307"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingSessions.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventory.js", ["308", "309", "310"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewPackingInventorySales.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SellProductPage.js", ["311", "312", "313", "314", "315", "316"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OrderListPage.js.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OwnerOrdersPage.js", [], ["317"], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesTeamOrdersPage.js", ["318", "319"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ShopAnalysisDashboard.js", ["320"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\OrderAnalysisPage.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductView.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesProductImageViewer.js", ["321"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewSuppliers.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\FabricInventoryDetail.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\CuttingRecordDetail.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\RoleBasedNavBar.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\SalesTeamNavBar.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\utils\\api.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\InvoicePreviewModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\OrderCoordinatorNavBar.js", ["322"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\PaymentModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\DeliveryModal.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\ShopDistrictAnalysis.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\utils\\auth.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\DashboardCard.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\ViewShops.js", ["323", "324", "325"], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\pages\\SalesReport.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\GitHub\\Pri_Fashion_\\frontend\\src\\components\\RevertOrderModal.js", [], [], {"ruleId": "326", "severity": 1, "message": "327", "line": 5, "column": 56, "nodeType": "328", "messageId": "329", "endLine": 5, "endColumn": 65}, {"ruleId": "326", "severity": 1, "message": "330", "line": 5, "column": 23, "nodeType": "328", "messageId": "329", "endLine": 5, "endColumn": 30}, {"ruleId": "331", "severity": 1, "message": "332", "line": 177, "column": 6, "nodeType": "333", "endLine": 177, "endColumn": 8, "suggestions": "334", "suppressions": "335"}, {"ruleId": "326", "severity": 1, "message": "336", "line": 76, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 76, "endColumn": 21}, {"ruleId": "326", "severity": 1, "message": "337", "line": 76, "column": 23, "nodeType": "328", "messageId": "329", "endLine": 76, "endColumn": 37}, {"ruleId": "326", "severity": 1, "message": "338", "line": 672, "column": 9, "nodeType": "328", "messageId": "329", "endLine": 672, "endColumn": 20}, {"ruleId": "331", "severity": 1, "message": "339", "line": 230, "column": 6, "nodeType": "333", "endLine": 230, "endColumn": 8, "suggestions": "340", "suppressions": "341"}, {"ruleId": "326", "severity": 1, "message": "342", "line": 11, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 11, "endColumn": 10}, {"ruleId": "331", "severity": 1, "message": "343", "line": 295, "column": 6, "nodeType": "333", "endLine": 295, "endColumn": 8, "suggestions": "344", "suppressions": "345"}, {"ruleId": "326", "severity": 1, "message": "346", "line": 27, "column": 20, "nodeType": "328", "messageId": "329", "endLine": 27, "endColumn": 31}, {"ruleId": "326", "severity": 1, "message": "347", "line": 36, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 36, "endColumn": 27}, {"ruleId": "326", "severity": 1, "message": "348", "line": 36, "column": 29, "nodeType": "328", "messageId": "329", "endLine": 36, "endColumn": 49}, {"ruleId": "331", "severity": 1, "message": "349", "line": 101, "column": 6, "nodeType": "333", "endLine": 101, "endColumn": 16, "suggestions": "350", "suppressions": "351"}, {"ruleId": "326", "severity": 1, "message": "352", "line": 9, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 9, "endColumn": 21}, {"ruleId": "326", "severity": 1, "message": "353", "line": 42, "column": 30, "nodeType": "328", "messageId": "329", "endLine": 42, "endColumn": 51}, {"ruleId": "326", "severity": 1, "message": "352", "line": 5, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 5, "endColumn": 21}, {"ruleId": "326", "severity": 1, "message": "354", "line": 9, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 9, "endColumn": 7}, {"ruleId": "326", "severity": 1, "message": "355", "line": 14, "column": 41, "nodeType": "328", "messageId": "329", "endLine": 14, "endColumn": 47}, {"ruleId": "326", "severity": 1, "message": "353", "line": 28, "column": 30, "nodeType": "328", "messageId": "329", "endLine": 28, "endColumn": 51}, {"ruleId": "326", "severity": 1, "message": "356", "line": 8, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 8, "endColumn": 15}, {"ruleId": "326", "severity": 1, "message": "357", "line": 35, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 35, "endColumn": 20}, {"ruleId": "326", "severity": 1, "message": "358", "line": 35, "column": 22, "nodeType": "328", "messageId": "329", "endLine": 35, "endColumn": 35}, {"ruleId": "326", "severity": 1, "message": "359", "line": 216, "column": 9, "nodeType": "328", "messageId": "329", "endLine": 216, "endColumn": 25}, {"ruleId": "326", "severity": 1, "message": "360", "line": 255, "column": 9, "nodeType": "328", "messageId": "329", "endLine": 255, "endColumn": 23}, {"ruleId": "326", "severity": 1, "message": "361", "line": 19, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 19, "endColumn": 20}, {"ruleId": "326", "severity": 1, "message": "362", "line": 10, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 10, "endColumn": 13}, {"ruleId": "326", "severity": 1, "message": "363", "line": 31, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 31, "endColumn": 9}, {"ruleId": "326", "severity": 1, "message": "364", "line": 33, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 33, "endColumn": 12}, {"ruleId": "326", "severity": 1, "message": "365", "line": 39, "column": 3, "nodeType": "328", "messageId": "329", "endLine": 39, "endColumn": 12}, {"ruleId": "326", "severity": 1, "message": "366", "line": 3, "column": 10, "nodeType": "328", "messageId": "329", "endLine": 3, "endColumn": 17}, {"ruleId": "326", "severity": 1, "message": "342", "line": 3, "column": 19, "nodeType": "328", "messageId": "329", "endLine": 3, "endColumn": 26}, {"ruleId": "326", "severity": 1, "message": "367", "line": 3, "column": 28, "nodeType": "328", "messageId": "329", "endLine": 3, "endColumn": 42}, {"ruleId": "326", "severity": 1, "message": "368", "line": 10, "column": 25, "nodeType": "328", "messageId": "329", "endLine": 10, "endColumn": 41}, {"ruleId": "331", "severity": 1, "message": "369", "line": 71, "column": 6, "nodeType": "333", "endLine": 71, "endColumn": 28, "suggestions": "370"}, {"ruleId": "326", "severity": 1, "message": "371", "line": 111, "column": 9, "nodeType": "328", "messageId": "329", "endLine": 111, "endColumn": 30}, {"ruleId": "326", "severity": 1, "message": "372", "line": 230, "column": 9, "nodeType": "328", "messageId": "329", "endLine": 230, "endColumn": 28, "suppressions": "373"}, {"ruleId": "326", "severity": 1, "message": "374", "line": 2, "column": 63, "nodeType": "328", "messageId": "329", "endLine": 2, "endColumn": 76}, {"ruleId": "326", "severity": 1, "message": "375", "line": 2, "column": 78, "nodeType": "328", "messageId": "329", "endLine": 2, "endColumn": 88}, {"ruleId": "326", "severity": 1, "message": "376", "line": 3, "column": 19, "nodeType": "328", "messageId": "329", "endLine": 3, "endColumn": 29}, {"ruleId": "326", "severity": 1, "message": "377", "line": 4, "column": 59, "nodeType": "328", "messageId": "329", "endLine": 4, "endColumn": 66}, {"ruleId": "326", "severity": 1, "message": "378", "line": 5, "column": 37, "nodeType": "328", "messageId": "329", "endLine": 5, "endColumn": 48}, {"ruleId": "326", "severity": 1, "message": "379", "line": 3, "column": 21, "nodeType": "328", "messageId": "329", "endLine": 3, "endColumn": 24}, {"ruleId": "326", "severity": 1, "message": "380", "line": 3, "column": 26, "nodeType": "328", "messageId": "329", "endLine": 3, "endColumn": 29}, {"ruleId": "326", "severity": 1, "message": "355", "line": 4, "column": 62, "nodeType": "328", "messageId": "329", "endLine": 4, "endColumn": 68}, "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'hasRole' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["381"], ["382"], "'isInfoModal' is assigned a value but never used.", "'setIsInfoModal' is assigned a value but never used.", "'getShopName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["383"], ["384"], "'FaBoxes' is defined but never used.", "React Hook useEffect has a missing dependency: 'timeFrame'. Either include it or remove the dependency array.", ["385"], ["386"], "'setUserRole' is assigned a value but never used.", "'editPasswordError' is assigned a value but never used.", "'setEditPasswordError' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["387"], ["388"], "'getUserRole' is defined but never used.", "'setIsInventoryManager' is assigned a value but never used.", "'Form' is defined but never used.", "'FaEdit' is defined but never used.", "'FaArrowRight' is defined but never used.", "'isDragging' is assigned a value but never used.", "'setIsDragging' is assigned a value but never used.", "'triggerFileInput' is assigned a value but never used.", "'setActiveImage' is assigned a value but never used.", "'totalItems' is assigned a value but never used.", "'FaBuilding' is defined but never used.", "'BsShop' is defined but never used.", "'BsGraphUp' is defined but never used.", "'FaHistory' is defined but never used.", "'FaStore' is defined but never used.", "'FaShoppingCart' is defined but never used.", "'setIsSidebarOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addProductToSelection'. Either include it or remove the dependency array.", ["389"], "'updateProductQuantity' is assigned a value but never used.", "'handleMarkDelivered' is assigned a value but never used.", ["390"], "'FaFileInvoice' is defined but never used.", "'FaDownload' is defined but never used.", "'FaChartPie' is defined but never used.", "'FaTimes' is defined but never used.", "'FaChartLine' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", {"desc": "391", "fix": "392"}, {"kind": "393", "justification": "394"}, {"desc": "395", "fix": "396"}, {"kind": "393", "justification": "394"}, {"desc": "397", "fix": "398"}, {"kind": "393", "justification": "394"}, {"desc": "399", "fix": "400"}, {"kind": "393", "justification": "394"}, {"desc": "401", "fix": "402"}, {"kind": "393", "justification": "394"}, "Update the dependencies array to be: [fetchProducts]", {"range": "403", "text": "404"}, "directive", "", "Update the dependencies array to be: [fetchData]", {"range": "405", "text": "406"}, "Update the dependencies array to be: [timeFrame]", {"range": "407", "text": "408"}, "Update the dependencies array to be: [fetchUsers, userRole]", {"range": "409", "text": "410"}, "Update the dependencies array to be: [addProductToSelection, preSelectedProductId]", {"range": "411", "text": "412"}, [5738, 5740], "[fetchProducts]", [7406, 7408], "[fetchData]", [11044, 11046], "[timeFrame]", [3791, 3801], "[fetchUsers, userRole]", [2958, 2980], "[addProductToSelection, preSelectedProductId]"]